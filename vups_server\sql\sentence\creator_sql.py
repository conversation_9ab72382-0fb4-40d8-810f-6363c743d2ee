# --- key_data_overview_table ---
create_key_data_overview_table_sql = """CREATE TABLE IF NOT EXISTS key_data_overview_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    play bigint,
    play_last bigint,
    visitor bigint,
    visitor_last bigint,
    fan bigint,
    fan_last bigint,
    like_count bigint,
    like_last bigint,
    fav bigint,
    fav_last bigint,
    coin bigint,
    coin_last bigint,
    dm bigint,
    dm_last bigint,
    comment bigint,
    comment_last bigint,
    share bigint,
    share_last bigint,
    log_date bigint,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, log_date)
);"""

insert_key_data_overview_table_sql = """INSERT INTO key_data_overview_table (
    uid, play, play_last, visitor, visitor_last, fan, fan_last, like_count, like_last, fav, fav_last,
    coin, coin_last, dm, dm_last, comment, comment_last, share, share_last, log_date, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
) ON CONFLICT (uid, log_date) DO UPDATE SET
    play = EXCLUDED.play,
    play_last = EXCLUDED.play_last,
    visitor = EXCLUDED.visitor,
    visitor_last = EXCLUDED.visitor_last,
    fan = EXCLUDED.fan,
    fan_last = EXCLUDED.fan_last,
    like_count = EXCLUDED.like_count,
    like_last = EXCLUDED.like_last,
    fav = EXCLUDED.fav,
    fav_last = EXCLUDED.fav_last,
    coin = EXCLUDED.coin,
    coin_last = EXCLUDED.coin_last,
    dm = EXCLUDED.dm,
    dm_last = EXCLUDED.dm_last,
    comment = EXCLUDED.comment,
    comment_last = EXCLUDED.comment_last,
    share = EXCLUDED.share,
    share_last = EXCLUDED.share_last,
    update_time = EXCLUDED.update_time;
"""

# --- weekly_key_data_overview_table ---
create_weekly_key_data_overview_table_sql = """CREATE TABLE IF NOT EXISTS weekly_key_data_overview_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    play_cnt_amount bigint,
    play_cnt_amount_pass_per bigint,
    play_cnt_amount_last bigint,
    play_cnt_amount_last_pass_per bigint,
    play_cnt_amount_change bigint,
    play_cnt_amount_med bigint,
    play_cnt_date bigint,
    play_cnt_tendency_list jsonb,
    interact_rate_amount bigint,
    interact_rate_amount_pass_per bigint,
    interact_rate_amount_last bigint,
    interact_rate_amount_last_pass_per bigint,
    interact_rate_amount_change bigint,
    interact_rate_amount_med bigint,
    interact_rate_date bigint,
    interact_rate_tendency_list jsonb,
    net_attention_cnt_amount bigint,
    net_attention_cnt_amount_pass_per bigint,
    net_attention_cnt_amount_last bigint,
    net_attention_cnt_amount_last_pass_per bigint,
    net_attention_cnt_amount_change bigint,
    net_attention_cnt_amount_med bigint,
    net_attention_cnt_date bigint,
    net_attention_cnt_tendency_list jsonb,
    interact_fans_per_amount bigint,
    interact_fans_per_amount_pass_per bigint,
    interact_fans_per_amount_last bigint,
    interact_fans_per_amount_last_pass_per bigint,
    interact_fans_per_amount_change bigint,
    interact_fans_per_amount_med bigint,
    interact_fans_per_date bigint,
    interact_fans_per_tendency_list jsonb,
    avs_num_amount bigint,
    avs_num_amount_pass_per bigint,
    avs_num_amount_last bigint,
    avs_num_amount_last_pass_per bigint,
    avs_num_amount_change bigint,
    avs_num_amount_med bigint,
    avs_num_date bigint,
    avs_num_tendency_list jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, play_cnt_date)
);"""

insert_weekly_key_data_overview_table_sql = """INSERT INTO weekly_key_data_overview_table (
    uid, play_cnt_amount, play_cnt_amount_pass_per, play_cnt_amount_last, play_cnt_amount_last_pass_per,
    play_cnt_amount_change, play_cnt_amount_med, play_cnt_date, play_cnt_tendency_list,
    interact_rate_amount, interact_rate_amount_pass_per, interact_rate_amount_last, interact_rate_amount_last_pass_per,
    interact_rate_amount_change, interact_rate_amount_med, interact_rate_date, interact_rate_tendency_list,
    net_attention_cnt_amount, net_attention_cnt_amount_pass_per, net_attention_cnt_amount_last, net_attention_cnt_amount_last_pass_per,
    net_attention_cnt_amount_change, net_attention_cnt_amount_med, net_attention_cnt_date, net_attention_cnt_tendency_list,
    interact_fans_per_amount, interact_fans_per_amount_pass_per, interact_fans_per_amount_last, interact_fans_per_amount_last_pass_per,
    interact_fans_per_amount_change, interact_fans_per_amount_med, interact_fans_per_date, interact_fans_per_tendency_list,
    avs_num_amount, avs_num_amount_pass_per, avs_num_amount_last, avs_num_amount_last_pass_per,
    avs_num_amount_change, avs_num_amount_med, avs_num_date, avs_num_tendency_list,
    create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25,
    $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43
) ON CONFLICT (uid, play_cnt_date) DO UPDATE SET
    play_cnt_amount = EXCLUDED.play_cnt_amount,
    play_cnt_amount_pass_per = EXCLUDED.play_cnt_amount_pass_per,
    play_cnt_amount_last = EXCLUDED.play_cnt_amount_last,
    play_cnt_amount_last_pass_per = EXCLUDED.play_cnt_amount_last_pass_per,
    play_cnt_amount_change = EXCLUDED.play_cnt_amount_change,
    play_cnt_amount_med = EXCLUDED.play_cnt_amount_med,
    play_cnt_tendency_list = EXCLUDED.play_cnt_tendency_list,
    interact_rate_amount = EXCLUDED.interact_rate_amount,
    interact_rate_amount_pass_per = EXCLUDED.interact_rate_amount_pass_per,
    interact_rate_amount_last = EXCLUDED.interact_rate_amount_last,
    interact_rate_amount_last_pass_per = EXCLUDED.interact_rate_amount_last_pass_per,
    interact_rate_amount_change = EXCLUDED.interact_rate_amount_change,
    interact_rate_amount_med = EXCLUDED.interact_rate_amount_med,
    interact_rate_date = EXCLUDED.interact_rate_date,
    interact_rate_tendency_list = EXCLUDED.interact_rate_tendency_list,
    net_attention_cnt_amount = EXCLUDED.net_attention_cnt_amount,
    net_attention_cnt_amount_pass_per = EXCLUDED.net_attention_cnt_amount_pass_per,
    net_attention_cnt_amount_last = EXCLUDED.net_attention_cnt_amount_last,
    net_attention_cnt_amount_last_pass_per = EXCLUDED.net_attention_cnt_amount_last_pass_per,
    net_attention_cnt_amount_change = EXCLUDED.net_attention_cnt_amount_change,
    net_attention_cnt_amount_med = EXCLUDED.net_attention_cnt_amount_med,
    net_attention_cnt_date = EXCLUDED.net_attention_cnt_date,
    net_attention_cnt_tendency_list = EXCLUDED.net_attention_cnt_tendency_list,
    interact_fans_per_amount = EXCLUDED.interact_fans_per_amount,
    interact_fans_per_amount_pass_per = EXCLUDED.interact_fans_per_amount_pass_per,
    interact_fans_per_amount_last = EXCLUDED.interact_fans_per_amount_last,
    interact_fans_per_amount_last_pass_per = EXCLUDED.interact_fans_per_amount_last_pass_per,
    interact_fans_per_amount_change = EXCLUDED.interact_fans_per_amount_change,
    interact_fans_per_amount_med = EXCLUDED.interact_fans_per_amount_med,
    interact_fans_per_date = EXCLUDED.interact_fans_per_date,
    interact_fans_per_tendency_list = EXCLUDED.interact_fans_per_tendency_list,
    avs_num_amount = EXCLUDED.avs_num_amount,
    avs_num_amount_pass_per = EXCLUDED.avs_num_amount_pass_per,
    avs_num_amount_last = EXCLUDED.avs_num_amount_last,
    avs_num_amount_last_pass_per = EXCLUDED.avs_num_amount_last_pass_per,
    avs_num_amount_change = EXCLUDED.avs_num_amount_change,
    avs_num_amount_med = EXCLUDED.avs_num_amount_med,
    avs_num_date = EXCLUDED.avs_num_date,
    avs_num_tendency_list = EXCLUDED.avs_num_tendency_list,
    update_time = EXCLUDED.update_time;
"""
