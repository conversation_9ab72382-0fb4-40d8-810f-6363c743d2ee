import asyncio
import json
import time
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from vups.logger import logger
from vups_server.sql.sentence import creator_sql
from vups_server.base.server_base import BaseServer
from vups_server.base.task_decorators import (
    pull_frequency, pull_schedule, round_robin_task,
    use_task_cookie, retry_on_failure, TaskPriority
)

class CreatorInfoServer(BaseServer):
    def __init__(self, uid="401315430", credential=None, db_conn=None):
        # Initialize base server with creator task type
        super().__init__(task_type="creator", uid=uid, credential=credential, db_conn=db_conn)

    async def initialize_async(self):
        await self._create_tables()

    async def _create_tables(self):
        """
        Create database tables.
        """
        logger.info("Creating database tables if not exists...")
        await self._execute_sql(creator_sql.create_key_data_overview_table_sql)

        logger.info("Database table creation complete.")

    async def get_server_status(self):
        """Get current server status"""
        return {
            'server_type': 'creator',
            'uid': self.uid,
            'credential_valid': self.validate_credential(),
            'task_type': self.task_type
        }

#   ----------------------------------  Overview  --------------------------------------

    @pull_schedule(hour=3, minute=0, priority=TaskPriority.NORMAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_key_data_overview(self):
        """
        url: https://member.bilibili.com/x/web/data/v2/overview/stat/num?period=-1&tab=0&tmid=&t=1757493254709

        args: cookie
            period = (-1 yestoday 0 7day 1 30day 2 90day 3 total)

        returns: 核心数据概览.
        {
            "data": {
                "play": 18702,
                "play_last": 22117,
                "visitor": 3448,
                "visitor_last": 3052,
                "fan": -120,
                "fan_last": -77,
                "like": 0,
                "like_last": 0,
                "fav": 0,
                "fav_last": 0,
                "coin": 0,
                "coin_last": 0,
                "dm": 0,
                "dm_last": 0,
                "comment": 0,
                "comment_last": 0,
                "share": 0,
                "share_last": 0,
                "elec": 0,
                "elec_last": 0,
                "vt": 0,
                "vt_last": 0,
                "log_date": 20250909
            }
        }

        """
        url = "https://member.bilibili.com/x/web/data/v2/overview/stat/num"
        result1 = await self._fetch_with_retry(url=url, params={"period": "-1", "tab": "0"})
        result2 = await self._fetch_with_retry(url=url, params={"period": "-1", "tab": "1"})
        result3 = await self._fetch_with_retry(url=url, params={"period": "-1", "tab": "2"})

        if result1 and result1.get('code') == 0 and result1.get('data'):

            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            data1 = result1['data']
            data2 = result2['data']
            data3 = result3['data']

            record = (
                self.uid,
                data1.get('play'),
                data1.get('play_last'),
                data1.get('visitor'),
                data1.get('visitor_last'),
                data1.get('fan'),
                data1.get('fan_last'),
                data2.get('like'),
                data2.get('like_last'),
                data2.get('fav'),
                data2.get('fav_last'),
                data2.get('coin'),
                data2.get('coin_last'),
                data3.get('dm'),
                data3.get('dm_last'),
                data3.get('comment'),
                data3.get('comment_last'),
                data3.get('share'),
                data3.get('share_last'),
                data1.get('log_date'),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_key_data_overview_table_sql, [record], "key_data_overview_table")

        return record

#   ----------------------------------  Overview  --------------------------------------

#   ----------------------------------  Weekly  ---------------------------------------
    @pull_schedule(hour=3, minute=0, day_of_week="tue", priority=TaskPriority.NORMAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_weekly_key_data_overview(self):
        """
        url: https://member.bilibili.com/x/web/data/v2/account_diagnose/overview?period=0&tmid=&t=*************

        args: cookie
            period = 0 7day
            tab = (0 play 1 interact 2 income)

        returns: data.数据周报的核心数据
            {
                "data": {
                    "play_cnt": {
                        "amount": 394273,
                        "amount_pass_per": 5524,
                        "amount_last": 592434,
                        "amount_last_pass_per": 5637,
                        "amount_change": -198161,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    },
                    "interact_rate": {
                        "amount": 1354,
                        "amount_pass_per": 9189,
                        "amount_last": 1036,
                        "amount_last_pass_per": 8410,
                        "amount_change": 3069,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    },
                    "net_attention_cnt": {
                        "amount": 2185,
                        "amount_pass_per": 5014,
                        "amount_last": 1856,
                        "amount_last_pass_per": 3883,
                        "amount_change": 1772,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    },
                    "interact_fans_per": {
                        "amount": 1392,
                        "amount_pass_per": 8645,
                        "amount_last": 1219,
                        "amount_last_pass_per": 8014,
                        "amount_change": 1419,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    },
                    "avs_num": {
                        "amount": 6,
                        "amount_pass_per": 8228,
                        "amount_last": 5,
                        "amount_last_pass_per": 7811,
                        "amount_change": 1,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    }
                }
            }

        """
        url = "https://member.bilibili.com/x/web/data/v2/account_diagnose/overview"
        result = await self._fetch_with_retry(url=url, params={"period": "0"})

        if result and result.get('code') == 0 and result.get('data'):
            data = result['data']
            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            record = (
                self.uid,
                data.get('play_cnt', {}).get('amount'),
                data.get('play_cnt', {}).get('amount_pass_per'),
                data.get('play_cnt', {}).get('amount_last'),
                data.get('play_cnt', {}).get('amount_last_pass_per'),
                data.get('play_cnt', {}).get('amount_change'),
                data.get('play_cnt', {}).get('amount_med'),
                data.get('play_cnt', {}).get('date'),
                data.get('play_cnt', {}).get('tendency_list'),
                data.get('interact_rate', {}).get('amount'),
                data.get('interact_rate', {}).get('amount_pass_per'),
                data.get('interact_rate', {}).get('amount_last'),
                data.get('interact_rate', {}).get('amount_last_pass_per'),
                data.get('interact_rate', {}).get('amount_change'),
                data.get('interact_rate', {}).get('amount_med'),
                data.get('interact_rate', {}).get('date'),
                data.get('interact_rate', {}).get('tendency_list'),
                data.get('net_attention_cnt', {}).get('amount'),
                data.get('net_attention_cnt', {}).get('amount_pass_per'),
                data.get('net_attention_cnt', {}).get('amount_last'),
                data.get('net_attention_cnt', {}).get('amount_last_pass_per'),
                data.get('net_attention_cnt', {}).get('amount_change'),
                data.get('net_attention_cnt', {}).get('amount_med'),
                data.get('net_attention_cnt', {}).get('date'),
                data.get('net_attention_cnt', {}).get('tendency_list'),
                data.get('interact_fans_per', {}).get('amount'),
                data.get('interact_fans_per', {}).get('amount_pass_per'),
                data.get('interact_fans_per', {}).get('amount_last'),
                data.get('interact_fans_per', {}).get('amount_last_pass_per'),
                data.get('interact_fans_per', {}).get('amount_change'),
                data.get('interact_fans_per', {}).get('amount_med'),
                data.get('interact_fans_per', {}).get('date'),
                
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_weekly_key_data_overview_table_sql, [record], "weekly_key_data_overview_table")

        return record
#   ----------------------------------  Weekly  ---------------------------------------
